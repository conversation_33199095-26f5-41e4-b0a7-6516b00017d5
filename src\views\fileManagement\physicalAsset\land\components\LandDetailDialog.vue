<!--土地资产详情弹窗组件-->
<template>
  <el-dialog 
    title="土地资产详情" 
    :visible.sync="dialogVisible" 
    width="80%" 
    append-to-body 
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-tabs type="border-card">
      <el-tab-pane label="基本信息">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">资产编号：</span>
              <span class="detail-value">{{ detailData.zcliAssetsNo || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">现状用途：</span>
              <span class="detail-value">{{ detailData.zcliUseDescribe || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">产权单位：</span>
              <span class="detail-value">{{ detailData.companyName || '-' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">土地权属证号：</span>
              <span class="detail-value">{{ detailData.zcliCertificateCode || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">具体土地位置：</span>
              <span class="detail-value">{{ detailData.zcliAddress || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">取得方式：</span>
              <span class="detail-value">{{ detailData.zcliType || '-' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">登记时间：</span>
              <span class="detail-value">{{ detailData.zcliDate || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">境内/境外：</span>
              <span class="detail-value">{{ detailData.zcliRange || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">土地性质：</span>
              <span class="detail-value">{{ detailData.zcliUseful || '-' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">是否两非资产：</span>
              <span class="detail-value">{{ detailData.zcliIfAssets || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">是否取得土地权属证明：</span>
              <span class="detail-value">{{ detailData.zcliIfExist || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">是否存在纠纷：</span>
              <span class="detail-value">{{ detailData.zcliIfDispute || '-' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">是否可处置：</span>
              <span class="detail-value">{{ detailData.zcliIfDispose || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">是否已抵押：</span>
              <span class="detail-value">{{ detailData.zcliIfMortgage || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">经办人：</span>
              <span class="detail-value">{{ detailData.zcliOperator || '-' }}</span>
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>

      <el-tab-pane label="位置信息">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">省份：</span>
              <span class="detail-value">{{ detailData.zcliProvince || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">城市：</span>
              <span class="detail-value">{{ detailData.zcliCity || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">区/县：</span>
              <span class="detail-value">{{ detailData.zcliCounty || '-' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <div class="detail-item">
              <span class="detail-label">详细地址：</span>
              <span class="detail-value">{{ detailData.zcliAddress || '-' }}</span>
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>

      <el-tab-pane label="财务信息">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">原值(万元)：</span>
              <span class="detail-value">{{ detailData.zcliBookValue || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">净值(万元)：</span>
              <span class="detail-value">{{ detailData.zcliNetbookValue || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">本年计提折旧(万元)：</span>
              <span class="detail-value">{{ detailData.zcliTotalDepreciation || '-' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">最近评估价值(万元)：</span>
              <span class="detail-value">{{ detailData.zcliEvaluateValue || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">最近评估日期：</span>
              <span class="detail-value">{{ detailData.zcliEvaluateDate || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">土地使用年限(年)：</span>
              <span class="detail-value">{{ detailData.zcliServiceLife || '-' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">折旧年限：</span>
              <span class="detail-value">{{ detailData.zcliDepreciableYear || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">土地总面积(㎡)：</span>
              <span class="detail-value">{{ detailData.zcliArea || '-' }}</span>
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>

      <el-tab-pane label="面积信息">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">土地总面积(㎡)：</span>
              <span class="detail-value">{{ detailData.zcliArea || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">自用面积(㎡)：</span>
              <span class="detail-value">{{ detailData.zcliStateZy || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">出租面积(㎡)：</span>
              <span class="detail-value">{{ detailData.zcliStateCz || '-' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">闲置面积(㎡)：</span>
              <span class="detail-value">{{ detailData.zcliStateXz || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">已抵押面积(㎡)：</span>
              <span class="detail-value">{{ detailData.zcliMortgage || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">容积率：</span>
              <span class="detail-value">{{ detailData.zcliPlotRatio || '-' }}</span>
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>

      <el-tab-pane label="管理信息">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">业务主管部门：</span>
              <span class="detail-value">{{ detailData.zcliDeptName || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">部门负责人：</span>
              <span class="detail-value">{{ detailData.zcliDepartmentLeader || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">部门负责人联系方式：</span>
              <span class="detail-value">{{ detailData.zcliDepartmentTel || '-' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">分管所(公司)领导：</span>
              <span class="detail-value">{{ detailData.zcliCompanyLeader || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">分管所(公司)领导联系方式：</span>
              <span class="detail-value">{{ detailData.zcliCompanyTel || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">经办人：</span>
              <span class="detail-value">{{ detailData.zcliOperator || '-' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">经办人联系方式：</span>
              <span class="detail-value">{{ detailData.zcliOperatorTel || '-' }}</span>
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>

      <el-tab-pane label="系统信息">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">创建时间：</span>
              <span class="detail-value">{{ detailData.createdTime || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">创建人：</span>
              <span class="detail-value">{{ detailData.createdBy || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">更新时间：</span>
              <span class="detail-value">{{ detailData.updatedTime || '-' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <div class="detail-item">
              <span class="detail-label">备注：</span>
              <span class="detail-value">{{ detailData.zcliRemark || '-' }}</span>
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>
    </el-tabs>

    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDialog">关 闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'LandDetailDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    detailData: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit('update:visible', false)
      this.$emit('close')
    },

    closeDialog() {
      this.handleClose()
    }
  }
}
</script>

<style scoped>
.detail-item {
  margin-bottom: 15px;
  line-height: 24px;
}
.detail-label {
  display: inline-block;
  min-width: 120px;
  color: #606266;
  font-weight: bold;
}
.detail-value {
  color: #303133;
}
</style>
