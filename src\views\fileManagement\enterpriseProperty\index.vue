<!--企业产权档案-->
<template>
  <div class="custom-table-container">
    <!-- 搜索区域 -->
    <div class="search-section">
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="企业名称:">
          <el-input
            v-model="searchForm.zcepraEnterpriseName"
            placeholder="请输入企业名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="经济行为类型:">
          <el-select
            v-model="searchForm.zcepraTypesOfEconomicBehavior"
            placeholder="请选择经济行为类型"
            clearable
            style="width: 180px"
          >
            <el-option label="类型名称" value="类型名称" />
            <el-option label="其他类型" value="其他类型" />
          </el-select>
        </el-form-item>
        <el-form-item label="历史时期:">
          <el-select
            v-model="searchForm.historicalPeriod"
            placeholder="请选择历史时期"
            clearable
            style="width: 150px"
          >
            <el-option label="时期1" value="时期1" />
            <el-option label="时期2" value="时期2" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-table
        :data="tableData"
        :height="height"
        border
        stripe
        style="width: 100%"
        v-loading="loading"
      >
        <el-table-column type="index" label="序号" align="center" width="95"/>
        <el-table-column prop="zcepraEnterpriseName" label="企业名称" align="center" />
        <el-table-column prop="zcepraTypesOfEconomicBehavior" label="经济行为类型" align="center" />
        <el-table-column prop="zcepraChangeInformation" label="变更信息" align="center" />
        <el-table-column prop="zcepraChangeDate" label="变更日期" align="center" />
        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="handleDetail(scope.row)"
              style="color: #409EFF"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        background
        class="el-pagination-a"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="searchForm.pageNo"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="searchForm.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      />
    </div>

    <!-- 详情弹窗 -->
    <el-dialog
      title="注销法人资格"
      :visible.sync="detailDialogVisible"
      width="80%"
      :close-on-click-modal="false"
      class="detail-dialog"
    >
      <div class="dialog-content">
        <!-- 标签页 -->
        <el-tabs v-model="activeTab" type="card">
          <el-tab-pane label="基础与清算" name="basic">
            <div class="tab-content">
              <!-- 文件表格 -->
              <div class="file-table-section">
                <el-table
                  :data="fileTableData"
                  border
                  style="width: 100%"
                  class="file-table"
                >
                  <el-table-column prop="category" label="" width="200" align="left">
                    <template slot-scope="scope">
                      <span class="category-label">{{ scope.row.category }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="files" label="" align="left">
                    <template slot-scope="scope">
                      <div class="file-links-cell">
                        <div v-for="(file, index) in scope.row.files" :key="index" class="file-link-item">
                          <a href="#" class="file-link" @click.prevent="handleFileClick(file)">{{ file }}</a>
                        </div>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>

              <!-- 国资委审核概况 -->
              <div class="review-section">
                <h3>国资委审核概况</h3>
                <div class="review-table">
                  <el-table
                    :data="reviewTableData"
                    border
                    style="width: 100%"
                    class="review-table-content"
                  >
                    <el-table-column prop="label" label="审核时间" width="200" align="center">
                      <template slot-scope="scope">
                        <span class="review-label">{{ scope.row.label }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="value" label="审核意见" align="center">
                      <template slot-scope="scope">
                        <span class="review-value">{{ scope.row.value }}</span>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="信息收集表" name="collection">
            <div class="tab-content">
              <p>信息收集表内容...</p>
            </div>
          </el-tab-pane>

          <el-tab-pane label="合规性审查" name="compliance">
            <div class="tab-content">
              <p>合规性审查内容...</p>
            </div>
          </el-tab-pane>

          <el-tab-pane label="注销企业+企业" name="cancellation">
            <div class="tab-content">
              <p>注销企业内容...</p>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="detailDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  enterprisePropertyGetList,
  enterprisePropertyGetDetail,
  enterprisePropertyUploadFile
} from '@/api/fileManagement/enterpriseProperty'

export default {
  name: "EnterpriseProperty",
  data() {
    return {
      height: !this.gheight?this.$baseTableHeight(1,1):this.gheight,
      // 搜索表单
      searchForm: {
        pageNo: 1,
        pageSize: 10,
        zcepraEnterpriseName: '', // 企业名称
        zcepraTypesOfEconomicBehavior: '', // 经济行为类型
        zcepraChangeInformation: '', // 变更信息
        zcepraChangeDateStart: '', // 变更日期开始
        zcepraChangeDateEnd: '', // 变更日期结束
      },

      // 加载状态
      loading: false,

      // 表格数据
      tableData: [],

      // 分页总数
      total: 0,

      // 详情弹窗
      detailDialogVisible: false,
      activeTab: 'basic',

      // 文件表格数据
      fileTableData: [
        {
          category: '自营业申请文件',
          files: ['申请主体文件.pdf', '申请主体文件.doc']
        },
        {
          category: '集团申请文件',
          files: ['关于XXXXXX公司产权变更申请申请.pdf']
        },
        {
          category: '清算报告',
          files: ['XXXXXX公司清算报告.pdf']
        },
        {
          category: '注销公告',
          files: ['XXXXXX公司注销公告.pdf']
        },
        {
          category: 'XXX附件',
          files: ['XXXXXX公司XXXX附件.pdf']
        },
        {
          category: '其他附件',
          files: [
            'XXXXXX公司申请报告.pdf',
            'XXXXXX公司备案登记表.pdf',
            'XXXXXX公司备案登记报告.pdf',
            'XXXXXX公司备案会议纪要.pdf'
          ]
        }
      ],

      // 审核表格数据
      reviewTableData: [
        {
          label: '2060 / 08 / 01 02:34',
          value: '资料齐全，同意办理'
        }
      ]
    }
  },

  methods: {
    // 搜索
    handleSearch() {
      this.searchForm.pageNo = 1
      this.loadData()
    },

    // 分页大小改变
    handleSizeChange(val) {
      this.searchForm.pageSize = val
      this.searchForm.pageNo = 1
      this.loadData()
    },

    // 当前页改变
    handleCurrentChange(val) {
      this.searchForm.pageNo = val
      this.loadData()
    },

    // 查看详情
    async handleDetail(row) {
      try {
        // 获取详情数据
        const detailRes = await enterprisePropertyGetDetail(row.zcepraId)
        if (detailRes.code === '200') {
          console.log('详情数据:', detailRes.data)
        }
        // 使用 mock 文件数据
        this.loadMockFileData()
        this.detailDialogVisible = true
        this.activeTab = 'basic'
      } catch (error) {
        console.error('获取详情失败:', error)
        // 使用 mock 文件数据
        this.loadMockFileData()
        this.detailDialogVisible = true
        this.activeTab = 'basic'
      }
    },

    // 加载 Mock 文件数据
    loadMockFileData() {
      const mockFiles = [
        { category: '自营业申请文件', fileName: '申请主体文件.pdf' },
        { category: '自营业申请文件', fileName: '申请主体文件.doc' },
        { category: '集团申请文件', fileName: '关于XXXXXX公司产权变更申请申请.pdf' },
        { category: '清算报告', fileName: 'XXXXXX公司清算报告.pdf' },
        { category: '注销公告', fileName: 'XXXXXX公司注销公告.pdf' },
        { category: 'XXX附件', fileName: 'XXXXXX公司XXXX附件.pdf' },
        { category: '其他附件', fileName: 'XXXXXX公司申请报告.pdf' },
        { category: '其他附件', fileName: 'XXXXXX公司备案登记表.pdf' },
        { category: '其他附件', fileName: 'XXXXXX公司备案登记报告.pdf' },
        { category: '其他附件', fileName: 'XXXXXX公司备案会议纪要.pdf' }
      ]
      this.updateFileTableData(mockFiles)
    },

    // 更新文件表格数据
    updateFileTableData(files) {
      // 根据文件类型分组
      const fileGroups = {
        '自营业申请文件': [],
        '集团申请文件': [],
        '清算报告': [],
        '注销公告': [],
        'XXX附件': [],
        '其他附件': []
      }

      files.forEach(file => {
        const category = file.category || '其他附件'
        if (fileGroups[category]) {
          fileGroups[category].push(file.fileName)
        } else {
          fileGroups['其他附件'].push(file.fileName)
        }
      })

      this.fileTableData = Object.keys(fileGroups).map(category => ({
        category,
        files: fileGroups[category]
      })).filter(item => item.files.length > 0)
    },

    // 文件点击处理
    handleFileClick(fileName) {
      this.$message.info(`点击了文件: ${fileName}`)
      // 这里可以添加文件下载或预览逻辑
      // 由于 beijing-API 中没有专门的文件下载接口，这里暂时只显示提示
    },

    // 加载数据
    async loadData() {
      this.loading = true
      try {
        const response = await enterprisePropertyGetList(this.searchForm)
        console.log(response);
        if (response.code == 200) {
          this.tableData = response.data.list || []
          this.total = response.data.total || 0
        } else {
          this.$message.error(response.msg || '获取数据失败')
        }
      } catch (error) {
        console.error('加载数据失败:', error)
        this.loading = false
      } finally {
        this.loading = false
      }
    },
  },

  mounted() {
    this.loadData()
  }
}
</script>

<style scoped>
.enterprise-property {
  padding: 20px;
  background: #f5f5f5;
  min-height: calc(100vh - 84px);
}

.page-header {
  margin-bottom: 20px;
}

.title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  display: flex;
  align-items: center;
}

.title i {
  margin-right: 8px;
  color: #e74c3c;
  font-size: 20px;
}

.search-section {
  background: white;
  padding: 20px;
  border-radius: 4px;
}

.search-form {
  margin: 0;
}

.search-form .el-form-item {
  margin-bottom: 0;
  margin-right: 20px;
}

.search-form .el-form-item__label {
  font-weight: normal;
  color: #666;
}

.table-section {
  background: white;
  padding: 20px;
  border-radius: 4px;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

.detail-dialog {
  .el-dialog__header {
    background: #f5f5f5;
    padding: 15px 20px;
  }

  .el-dialog__title {
    font-size: 16px;
    font-weight: bold;
  }

  .el-dialog__body {
    padding: 0;
  }
}

.dialog-content {
  padding: 20px;
}

.tab-content {
  padding: 20px 0;
}

/* 文件表格样式 */
.file-table-section {
  margin-bottom: 30px;
}

.file-table {
  border: 1px solid #e4e7ed;
}

.file-table .el-table__header th {
  background: #f8f9fa;
  border: none;
  height: 0;
  padding: 0;
}

.file-table .el-table__header th .cell {
  display: none;
}

.file-table .el-table__body td {
  border-right: 1px solid #e4e7ed;
  padding: 15px 20px;
  vertical-align: top;
}

.category-label {
  font-weight: bold;
  color: #333;
  font-size: 14px;
  display: block;
  text-align: left;
}

.file-links-cell {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.file-link-item {
  margin-bottom: 5px;
}

.file-link {
  color: #409EFF;
  text-decoration: none;
  font-size: 13px;
  cursor: pointer;
}

.file-link:hover {
  text-decoration: underline;
}

.review-section {
  border-top: 2px solid #eee;
  padding-top: 20px;
}

.review-section h3 {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 15px;
  text-align: center;
}

.review-table {
  margin-top: 15px;
}

.review-table-content {
  border: 1px solid #e4e7ed;
}

.review-table-content .el-table__header th {
  background: #f8f9fa;
  color: #333;
  font-weight: bold;
  text-align: center;
  border-right: 1px solid #e4e7ed;
}

.review-table-content .el-table__body td {
  border-right: 1px solid #e4e7ed;
  padding: 15px 20px;
  text-align: center;
}

.review-label {
  color: #333;
  font-size: 13px;
}

.review-value {
  color: #333;
  font-size: 13px;
}

.dialog-footer {
  text-align: center;
  padding: 15px 0;
}

/* 表格样式优化 */
.el-table {
  font-size: 13px;
}

.el-table th {
  background: #f8f9fa;
  color: #333;
  font-weight: bold;
}

.el-table td {
  padding: 12px 0;
}

/* 标签页样式 */
.el-tabs--card > .el-tabs__header .el-tabs__nav {
  border: 1px solid #e4e7ed;
  border-radius: 4px 4px 0 0;
}

.el-tabs--card > .el-tabs__header .el-tabs__item {
  border-right: 1px solid #e4e7ed;
  color: #666;
}

.el-tabs--card > .el-tabs__header .el-tabs__item.is-active {
  background: #409EFF;
  color: white;
  border-color: #409EFF;
}
</style>
