<!--实物资产档案 - 土地-->
<template>
  <div class="land-container">
    <!-- 顶部统计卡片 -->
    <div class="statistics-cards">
      <div class="stat-card">
        <div class="icon-wrapper red-bg">
          <i class="el-icon-map-location" />
        </div>
        <div class="stat-info">
          <div class="stat-title">总土地面积</div>
          <div class="stat-value red-text">{{ statistics.totalCount || 0 }}<span>宗</span></div>
        </div>
      </div>

      <div class="stat-card">
        <div class="icon-wrapper orange-bg">
          <i class="el-icon-money" />
        </div>
        <div class="stat-info">
          <div class="stat-title">土地总价值</div>
          <div class="stat-value orange-text">{{ statistics.totalValue || 0 }}<span>亿元</span></div>
        </div>
      </div>

      <div class="stat-card">
        <div class="icon-wrapper blue-bg">
          <i class="el-icon-data-line" />
        </div>
        <div class="stat-info">
          <div class="stat-title">本年度处置项目数</div>
          <div class="stat-value blue-text">{{ statistics.disposalCount || 0 }}<span>项</span></div>
        </div>
      </div>

      <div class="stat-card">
        <div class="icon-wrapper purple-bg">
          <i class="el-icon-key" />
        </div>
        <div class="stat-info">
          <div class="stat-title">本年度租赁项目数</div>
          <div class="stat-value purple-text">{{ statistics.leaseCount || 0 }}<span>项</span></div>
        </div>
      </div>
    </div>

    <!-- 搜索表单 -->
    <div class="search-form">
      <el-form :inline="true" :model="searchForm" class="form-inline" label-width="100px">
        <el-form-item label="资产编号:">
          <el-input v-model="searchForm.zcliAssetsNo" placeholder="请输入资产编号" class="inputW" />
        </el-form-item>
        <el-form-item label="土地权属证号:">
          <el-input v-model="searchForm.zcliCertificateCode" placeholder="请输入土地权属证明编号" class="inputW" />
        </el-form-item>
        <el-form-item label="产权单位:">
          <el-input v-model="searchForm.companyName" placeholder="请输入产权单位" class="inputW" />
        </el-form-item>
        <el-form-item label="经办人:">
          <el-input v-model="searchForm.zcliOperator" placeholder="请输入经办人" class="inputW" />
        </el-form-item>
        <el-form-item label="所在城市:">
          <el-input v-model="searchForm.zcliCity" placeholder="请输入所在城市" class="inputW" />
        </el-form-item>
        <el-form-item label="土地位置:">
          <el-input v-model="searchForm.zcliAddress" placeholder="请输入具体土地位置" class="inputW" />
        </el-form-item>
        <el-form-item label="现状用途:">
          <el-select v-model="searchForm.zcliUseDescribe" placeholder="请选择现状用途" class="inputW" clearable>
            <el-option label="工业用地" value="工业用地" />
            <el-option label="商业用地" value="商业用地" />
            <el-option label="住宅用地" value="住宅用地" />
            <el-option label="办公用地" value="办公用地" />
            <el-option label="其他" value="其他" />
          </el-select>
        </el-form-item>
        <el-form-item label="是否可处置:">
          <el-select v-model="searchForm.zcliIfDispose" placeholder="请选择" class="inputW" clearable>
            <el-option label="是" value="是" />
            <el-option label="否" value="否" />
          </el-select>
        </el-form-item>
        <el-form-item label="是否存在纠纷:">
          <el-select v-model="searchForm.zcliIfDispute" placeholder="请选择" class="inputW" clearable>
            <el-option label="是" value="是" />
            <el-option label="否" value="否" />
          </el-select>
        </el-form-item>
        <el-form-item label="是否已抵押:">
          <el-select v-model="searchForm.zcliIfMortgage" placeholder="请选择" class="inputW" clearable>
            <el-option label="是" value="是" />
            <el-option label="否" value="否" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
          <el-button @click="onExport">导出</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <el-table :data="tableData" :height="280" border style="width: 100%;margin-bottom: 20px;" v-loading="loading" row-key="zcliId">
      <el-table-column type="index" label="序号" width="60" align="center" />
      <el-table-column prop="zcliAssetsNo" label="资产编号" width="100" show-overflow-tooltip />
      <el-table-column prop="zcliUseDescribe" label="现状用途" width="120" show-overflow-tooltip />
      <el-table-column prop="companyName" label="产权单位" width="120" show-overflow-tooltip />
      <el-table-column prop="zcliCertificateCode" label="土地权属证号" width="120" show-overflow-tooltip />
      <el-table-column prop="zcliAddress" label="具体土地位置" width="140" show-overflow-tooltip />
      <el-table-column prop="zcliType" label="取得方式" width="100" show-overflow-tooltip />
      <el-table-column prop="zcliDate" label="登记时间" width="120" show-overflow-tooltip />
      <el-table-column prop="zcliArea" label="土地总面积(㎡)" width="120" />
      <el-table-column prop="zcliBookValue" label="原值(万元)" width="120" show-overflow-tooltip />
      <el-table-column prop="zcliNetbookValue" label="净值(万元)" width="120" show-overflow-tooltip />
      <el-table-column prop="zcliTotalDepreciation" label="本年计提折旧(万元)" width="140" show-overflow-tooltip />
      <el-table-column prop="zcliRange" label="境内/境外" width="100" show-overflow-tooltip />
      <el-table-column prop="zcliIfAssets" label="是否两非资产" width="120" />
      <el-table-column prop="zcliIfExist" label="是否取得土地权属证明" width="150" show-overflow-tooltip />
      <el-table-column prop="zcliIfDispute" label="是否存在纠纷" width="120" show-overflow-tooltip />
      <el-table-column prop="zcliIfDispose" label="是否可处置" width="120" show-overflow-tooltip />
      <el-table-column prop="zcliIfMortgage" label="是否已抵押" width="120" show-overflow-tooltip />
      <el-table-column prop="zcliOperator" label="经办人" width="100" show-overflow-tooltip />
      <el-table-column prop="zcliOperatorTel" label="经办人联系方式" width="120" show-overflow-tooltip />
      <el-table-column prop="zcliProvince" label="省份" width="100" show-overflow-tooltip />
      <el-table-column prop="zcliCity" label="城市" width="100" show-overflow-tooltip />
      <el-table-column prop="zcliCounty" label="区/县" width="100" show-overflow-tooltip />
      <el-table-column prop="zcliDeptName" label="业务主管部门" width="140" show-overflow-tooltip />
      <el-table-column prop="zcliDepartmentLeader" label="部门负责人" width="120" show-overflow-tooltip />
      <el-table-column prop="zcliDepartmentTel" label="部门负责人联系方式" width="140" show-overflow-tooltip />
      <el-table-column prop="zcliCompanyLeader" label="分管所(公司)领导" width="140" show-overflow-tooltip />
      <el-table-column prop="zcliCompanyTel" label="分管所(公司)领导联系方式" width="160" show-overflow-tooltip />
      <el-table-column prop="zcliEvaluateValue" label="最近评估价值(万元)" width="140" show-overflow-tooltip />
      <el-table-column prop="zcliEvaluateDate" label="最近评估日期" width="120" show-overflow-tooltip />
      <el-table-column prop="zcliServiceLife" label="土地使用年限(年)" width="120" show-overflow-tooltip />
      <el-table-column prop="zcliDepreciableYear" label="折旧年限" width="100" show-overflow-tooltip />
      <el-table-column prop="zcliRemark" label="备注" width="150" show-overflow-tooltip />
      <el-table-column prop="createdTime" label="创建时间" width="160" show-overflow-tooltip />
      <el-table-column prop="createdBy" label="创建人" width="100" show-overflow-tooltip />
      <el-table-column prop="updatedTime" label="更新时间" width="160" show-overflow-tooltip />
      <el-table-column label="操作" width="80" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="handleDetail(scope.row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination background class="el-pagination-a" @current-change="handleCurrentChange" @size-change="handleSizeChange" :current-page="currentPage" :page-size="pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" :total="total" />

    <!-- 详情弹窗组件 -->
    <land-detail-dialog :visible.sync="detailDialogVisible" :detail-data="detailData" @close="handleDetailDialogClose" />
  </div>
</template>

<script>
import { getLandsList, getLandDetail, exportLands, getLandsStats } from '@/api/land'
import LandDetailDialog from './components/LandDetailDialog.vue'

export default {
  name: "LandIndex",
  components: {
    LandDetailDialog
  },
  data () {
    return {
      searchForm: {
        zcliAssetsNo: '',
        zcliCertificateCode: '',
        companyName: '',
        zcliOperator: '',
        zcliCity: '',
        zcliAddress: '',
        zcliUseDescribe: '',
        zcliIfDispose: '',
        zcliIfDispute: '',
        zcliIfMortgage: ''
      },
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      loading: false,
      statistics: {
        totalCount: 0,
        totalValue: 0,
        totalArea: 0,
        disposalCount: 0,
        leaseCount: 0
      },
      detailData: {},
      detailDialogVisible: false
    }
  },
  created () {
    this.fetchData()
    this.fetchStatistics()
  },
  methods: {
    fetchData () {
      this.loading = true
      const query = {
        ...this.searchForm,
        pageNo: this.currentPage,
        pageSize: this.pageSize
      }

      getLandsList(query).then(response => {
        if (response && response.data) {
          this.tableData = response.data.list || []
          this.total = response.data.total || 0
        } else {
          this.tableData = []
          this.total = 0
        }
        this.loading = false
      }).catch(() => {
        this.loading = false
        this.$message.error('获取数据失败')
        // 使用 Mock 数据
        this.loadMockData()
      })
    },

    fetchStatistics () {
      getLandsStats().then(response => {
        if (response && response.data) {
          this.statistics = {
            totalCount: response.data.TOTAL_LAND || 0,
            totalValue: response.data.TOTAL_VALUE || 0,
            totalArea: response.data.totalArea || 0,
            disposalCount: response.data.disposalCount || 0,
            leaseCount: response.data.leaseCount || 0
          }
        }
      }).catch(() => {
        this.$message.error('获取统计数据失败')
        // 使用 Mock 统计数据
        this.statistics = {
          totalCount: 156,
          totalValue: 23.5,
          totalArea: 1250000,
          disposalCount: 8,
          leaseCount: 12
        }
      })
    },

    loadMockData () {
      this.tableData = [
        {
          zcliId: '1',
          zcliAssetsNo: 'TD001',
          zcliUseDescribe: '工业用地',
          companyName: '中电集团信息系统有限公司',
          zcliCertificateCode: 'NJ2023001',
          zcliAddress: '南京市江宁区高新园区',
          zcliType: '出让',
          zcliDate: '2020-03-15',
          zcliArea: 15000,
          zcliBookValue: 2500,
          zcliNetbookValue: 2300,
          zcliTotalDepreciation: 200,
          zcliRange: '境内',
          zcliIfAssets: '否',
          zcliIfExist: '是',
          zcliIfDispute: '否',
          zcliIfDispose: '是',
          zcliIfMortgage: '否',
          zcliOperator: '张三',
          zcliOperatorTel: '13800138001',
          zcliProvince: '江苏省',
          zcliCity: '南京市',
          zcliCounty: '江宁区',
          zcliDeptName: '资产管理部',
          zcliDepartmentLeader: '李四',
          zcliDepartmentTel: '025-12345678',
          zcliCompanyLeader: '王五',
          zcliCompanyTel: '025-87654321',
          zcliEvaluateValue: 2800,
          zcliEvaluateDate: '2023-12-01',
          zcliServiceLife: '50',
          zcliDepreciableYear: '50',
          zcliRemark: '优质工业用地',
          createdTime: '2023-01-01 10:00:00',
          createdBy: '系统管理员',
          updatedTime: '2023-12-01 15:30:00'
        },
        {
          zcliId: '2',
          zcliAssetsNo: 'TD002',
          zcliUseDescribe: '商业用地',
          companyName: '中电大厦（集团）有限公司',
          zcliCertificateCode: 'SZ2023002',
          zcliAddress: '苏州市工业园区',
          zcliType: '出让',
          zcliDate: '2019-08-20',
          zcliArea: 8000,
          zcliBookValue: 4500,
          zcliNetbookValue: 4200,
          zcliTotalDepreciation: 300,
          zcliRange: '境内',
          zcliIfAssets: '否',
          zcliIfExist: '是',
          zcliIfDispute: '否',
          zcliIfDispose: '否',
          zcliIfMortgage: '否',
          zcliOperator: '赵六',
          zcliOperatorTel: '13900139002',
          zcliProvince: '江苏省',
          zcliCity: '苏州市',
          zcliCounty: '工业园区',
          zcliDeptName: '资产管理部',
          zcliDepartmentLeader: '钱七',
          zcliDepartmentTel: '0512-12345678',
          zcliCompanyLeader: '孙八',
          zcliCompanyTel: '0512-87654321',
          zcliEvaluateValue: 5200,
          zcliEvaluateDate: '2023-11-15',
          zcliServiceLife: '50',
          zcliDepreciableYear: '50',
          zcliRemark: '核心商业区域',
          createdTime: '2023-01-15 09:00:00',
          createdBy: '系统管理员',
          updatedTime: '2023-11-15 14:20:00'
        }
      ]
      this.total = 2
    },

    onSearch () {
      this.currentPage = 1
      this.fetchData()
    },

    resetQuery () {
      this.currentPage = 1
      this.searchForm = {
        zchiAssetsNo: '',
        zchiCertificateCode: '',
        companyName: '',
        zchiOperator: '',
        zchiCity: '',
        zchiBusinessDirection: '',
        zchiUseDescribe: '',
        zchiIfDispose: ''
      }
      this.fetchData()
    },

    onExport () {
      const query = {
        ...this.searchForm
      }
      exportLands(query).then(() => {
        this.$message.success('导出成功')
      }).catch(() => {
        this.$message.error('导出失败')
      })
    },

    handleDetail (row) {
      console.log('点击详情按钮，行数据:', row)
      if (!row.zcliId) {
        this.$message.error('数据异常：缺少ID字段')
        return
      }

      this.loading = true
      getLandDetail(row.zcliId).then(response => {
        console.log('详情接口响应:', response)
        if (response && response.data) {
          this.detailData = response.data
          this.detailDialogVisible = true
        }
        this.loading = false
      }).catch((error) => {
        console.log('详情接口失败:', error)
        this.loading = false
        this.$message.warning('获取详情失败，使用当前行数据')
        // 使用当前行数据作为详情数据
        this.detailData = row
        this.detailDialogVisible = true
      })
    },

    handleDetailDialogClose () {
      this.detailDialogVisible = false
      this.detailData = {}
    },

    handleSizeChange (val) {
      this.pageSize = val
      this.fetchData()
    },

    handleCurrentChange (val) {
      this.currentPage = val
      this.fetchData()
    }
  }
}
</script>

<style lang="scss" scoped>
.inputW {
  width: 270px;
}

.land-container {
  padding: 16px;
  background-color: #f5f7fa;
}

/* 顶部统计卡片样式 */
.statistics-cards {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.stat-card {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-right: 15px;
}

.stat-card:last-child {
  margin-right: 0;
}

.icon-wrapper {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.icon-wrapper i {
  font-size: 24px;
  color: #fff;
}

.red-bg {
  background-color: rgba(245, 108, 108, 0.2);
}

.orange-bg {
  background-color: rgba(230, 162, 60, 0.2);
}

.blue-bg {
  background-color: rgba(64, 158, 255, 0.2);
}

.purple-bg {
  background-color: rgba(103, 194, 58, 0.2);
}

.stat-info {
  flex: 1;
}

.stat-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
}

.stat-value span {
  font-size: 14px;
  margin-left: 4px;
}

.red-text {
  color: #f56c6c;
}

.orange-text {
  color: #e6a23c;
}

.blue-text {
  color: #409eff;
}

.purple-text {
  color: #67c23a;
}

/* 搜索表单样式 */
.search-form {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
}

/* 数据表格样式 */
.data-table {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}
</style>
